import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { customAlphabet } from 'nanoid';
import { IsNull, Not } from 'typeorm';
import { DatabaseService, FlowProcessStep } from '@core/database';
import { CreateFlowProcessStepDto } from '../flow-process-steps.dto';
import { FlowProcessStepsService } from '../flow-process-steps.service';

const alphabet = '0123456789abcdefghijklmnopqrstuvwxyz';
const nanoid = customAlphabet(alphabet, 6);

@Injectable()
export class CreateFlowProcessStepUseCase implements UseCase {
  private readonly logger = new Logger('CreateFlowProcessStep');

  constructor(
    private readonly stepService: FlowProcessStepsService,
    private readonly db: DatabaseService,
  ) {}

  async generateMaterializedPath(parentId: EntityId | null): Promise<string> {
    let mPath = '';

    const stepPath = nanoid();

    if (!parentId) {
      mPath = stepPath;
    } else {
      const parent = await this.stepService.findOneBy({ id: parentId });
      if (!parent) throw new NotFoundException('Parent not found', 'PARENT_NOT_FOUND');

      mPath = parent.mPath + '/' + stepPath;
    }

    return mPath;
  }

  /**
   * Potential issue with recursion.
   */
  async recursiveGenerateMaterializedPath(processId: EntityId, parentId: EntityId | null): Promise<string> {
    const mPath = await this.generateMaterializedPath(parentId);

    const existingStepWithSamePath = await this.stepService.findOneBy({ processId, mPath });
    if (existingStepWithSamePath) return await this.recursiveGenerateMaterializedPath(processId, parentId);

    return mPath;
  }

  async execute(processId: EntityId, dto: CreateFlowProcessStepDto): Promise<FlowProcessStep> {
    /** Generate mPath */
    const mPath = await this.recursiveGenerateMaterializedPath(processId, dto.parentId);

    const step = await this.db.flowProcessSteps.manager.transaction(async transactionalEntityManager => {
      /** Create step */
      const step = transactionalEntityManager.create(FlowProcessStep, {
        ...this.stepService.normalizeEntityValues({ ...dto, mPath }),
        flowProcess: { id: processId },
      });

      await transactionalEntityManager.save(step);

      this.logger.verbose({ msg: 'Creating step', data: { step } });

      /** Find step with same prevId */
      const stepWithSamePrevId = await transactionalEntityManager.findOne(FlowProcessStep, {
        where: {
          id: Not(step.id),
          flowProcess: { id: processId },
          prevId: dto.prevId === null ? IsNull() : step.id,
        },
      });

      if (stepWithSamePrevId) {
        this.logger.verbose({ msg: 'Found step with same prevId', data: { stepWithSamePrevId } });

        /** Shift stepWithSamePrevId below */
        await transactionalEntityManager.update(FlowProcessStep, stepWithSamePrevId.id, {
          prevId: step.id,
        });
      }

      return step;
    });

    return step;
  }
}
