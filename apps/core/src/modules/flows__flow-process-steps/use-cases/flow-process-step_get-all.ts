import { Injectable } from '@nestjs/common';
import { FlowProcessStep } from '@core/database';
import { FlowProcessStepsService } from '../flow-process-steps.service';

@Injectable()
export class GetAllFlowProcessStepsUseCase implements UseCase {
  constructor(private readonly stepService: FlowProcessStepsService) {}

  async execute({ flowProcessId }: { flowProcessId: EntityId }): Promise<FlowProcessStep[]> {
    return await this.stepService.findAll({ processId: flowProcessId });
  }
}
