import { Injectable, NotFoundException } from '@nestjs/common';
import { FlowProcessStep } from '@core/database';
import { FlowProcessStepsService } from '../flow-process-steps.service';

@Injectable()
export class GetFlowProcessStepByIdUseCase implements UseCase {
  constructor(private readonly stepService: FlowProcessStepsService) {}

  async execute(id: EntityId): Promise<FlowProcessStep> {
    const flowProcessStep = await this.stepService.findOneBy({ id });

    if (!flowProcessStep)
      throw new NotFoundException('Flow process step not found', 'FLOW_PROCESS_STEP_NOT_FOUND');

    return flowProcessStep;
  }
}
