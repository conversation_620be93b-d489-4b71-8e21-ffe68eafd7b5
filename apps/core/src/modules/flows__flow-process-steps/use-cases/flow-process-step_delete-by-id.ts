import { Injectable, NotFoundException } from '@nestjs/common';
import { FlowProcessStep } from '@core/database';
import { FlowProcessStepsService } from '../flow-process-steps.service';

@Injectable()
export class DeleteFlowProcessStepByIdUseCase implements UseCase {
  constructor(private readonly stepService: FlowProcessStepsService) {}

  async execute({ processId, stepId }: { processId: EntityId; stepId: EntityId }): Promise<FlowProcessStep> {
    const step = await this.stepService.findOneBy({ id: stepId });

    if (!step) throw new NotFoundException('Flow process step not found', 'FLOW_PROCESS_STEP_NOT_FOUND');

    await this.stepService.deleteChildrenByMPath({
      processId,
      mPath: step.mPath,
    });

    const nextStep = await this.stepService.findOneBy({ processId, prevId: stepId });
    if (nextStep) {
      await this.stepService.updateFlowProcessStep(nextStep, { prevId: step.prevId });
    }

    return await this.stepService.deleteFlowProcessStep(step);
  }
}
