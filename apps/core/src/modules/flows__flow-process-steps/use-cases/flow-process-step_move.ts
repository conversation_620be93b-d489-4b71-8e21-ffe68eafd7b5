import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { FlowProcessStep } from '@core/database';
import { MoveFlowProcessStepDto } from '../flow-process-steps.dto';
import { FlowProcessStepsService } from '../flow-process-steps.service';

@Injectable()
export class MoveFlowProcessStepUseCase implements UseCase {
  private readonly logger = new Logger('MoveFlowProcessStep');

  constructor(private readonly stepService: FlowProcessStepsService) {}

  async execute(id: EntityId, processId: EntityId, dto: MoveFlowProcessStepDto): Promise<FlowProcessStep> {
    const { parentId: targetParentId, prevId: targetPrevId } = dto;

    // TODO: optimize

    this.logger.verbose(
      `Moving step: targetNextStep query data { parentId: ${targetParentId}, prevId: ${targetPrevId} }`,
    );

    // TODO: find many and then use find by result array

    const [step, nextStep, targetNextStep, targetParentStep] = await Promise.all([
      this.stepService.findOneBy({ processId, id }),
      this.stepService.findOneBy({ processId, prevId: id }),
      this.stepService.findOneBy({ processId, parentId: targetParentId, prevId: targetPrevId }),
      targetParentId ? this.stepService.findOneBy({ processId, id: targetParentId }) : null,
    ]);

    this.logger.verbose({
      msg: 'Moving step: selected data',
      data: { step, nextStep, targetNextStep, targetParentStep },
    });

    if (!step) throw new NotFoundException('Flow process step not found', 'FLOW_PROCESS_STEP_NOT_FOUND');

    // TODO: recreate and revalidate stepPath
    const stepPath = step.mPath.split('/').pop();

    /** Shift nextStep above */
    if (nextStep) {
      this.logger.verbose(
        `Moving step: shifting nextStep (id: ${nextStep.id}) above (update prevId: ${step.prevId})`,
      );
      await this.stepService.updateFlowProcessStep(nextStep, { prevId: step.prevId });
    }

    /** Move step */
    const stepMPath = targetParentStep ? targetParentStep.mPath + '/' + stepPath : stepPath;

    this.logger.verbose(
      `Moving step: moving step (id: ${step.id}, mPath: ${stepMPath}) to new parent (parentId: ${targetParentId}) and prev (prevId: ${targetPrevId})`,
    );

    const modifiedStep = await this.stepService.updateFlowProcessStep(step, {
      parentId: targetParentId,
      prevId: targetPrevId,
      mPath: stepMPath,
    });

    /** Shift targetNextStep below */
    if (targetNextStep) {
      this.logger.verbose(
        `Moving step: shifting targetStep (id: ${targetNextStep.id}) below (to ${modifiedStep.id})`,
      );
      await this.stepService.updateFlowProcessStep(targetNextStep, { prevId: modifiedStep.id });
    }

    /** Update mPath for children */
    this.logger.verbose(
      `Moving step: updating mPath for children (old: ${step.mPath}, new: ${modifiedStep.mPath})`,
    );
    await this.stepService.updateMPathForChildren(processId, step.mPath, modifiedStep.mPath);

    return modifiedStep;
  }
}
