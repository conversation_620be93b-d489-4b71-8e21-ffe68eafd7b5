import { Injectable, NotFoundException } from '@nestjs/common';
import { FlowProcessStep } from '@core/database';
import { PartialUpdateFlowProcessStepDto, UpdateFlowProcessStepDto } from '../flow-process-steps.dto';
import { FlowProcessStepsService } from '../flow-process-steps.service';

@Injectable()
export class UpdateFlowProcessStepUseCase implements UseCase {
  constructor(private readonly stepService: FlowProcessStepsService) {}

  async execute(
    id: EntityId,
    dto: UpdateFlowProcessStepDto | PartialUpdateFlowProcessStepDto,
  ): Promise<FlowProcessStep> {
    const { name, description } = dto;

    const flowProcessStep = await this.stepService.findOneBy({ id });
    if (!flowProcessStep)
      throw new NotFoundException('Flow process step not found', 'FLOW_PROCESS_STEP_NOT_FOUND');

    return await this.stepService.updateFlowProcessStep(flowProcessStep, { name, description });
  }
}
