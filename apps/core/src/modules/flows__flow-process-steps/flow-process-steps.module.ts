import { Modu<PERSON> } from '@nestjs/common';
import { DatabaseModule } from '@core/database';
import { FlowProcessStepsController } from './flow-process-steps.controller';
import { FlowProcessStepsService } from './flow-process-steps.service';
import {
  GetAllFlowProcessStepsUseCase,
  CreateFlowProcessStepUseCase,
  GetFlowProcessStepByIdUseCase,
  UpdateFlowProcessStepUseCase,
  MoveFlowProcessStepUseCase,
  DeleteFlowProcessStepByIdUseCase,
} from './use-cases';

@Module({
  imports: [DatabaseModule],
  providers: [
    FlowProcessStepsService,

    GetAllFlowProcessStepsUseCase,
    CreateFlowProcessStepUseCase,
    GetFlowProcessStepByIdUseCase,
    UpdateFlowProcessStepUseCase,
    MoveFlowProcessStepUseCase,
    DeleteFlowProcessStepByIdUseCase,
  ],
  controllers: [FlowProcessStepsController],
})
export class FlowProcessStepsModule {}
