import { Injectable } from '@nestjs/common';
import { IsNull, Like, Repository } from 'typeorm';
import { DatabaseService, FlowProcessStep } from '@core/database';
import { FlowProcessStepModel } from '@core/models';

type FindOneOptions = {
  id?: EntityId;
  mPath?: string;
  processId?: EntityId;
  parentId?: EntityId | null;
  prevId?: EntityId | null;
};

@Injectable()
export class FlowProcessStepsService {
  public readonly repository: Repository<FlowProcessStep>;

  constructor(private readonly db: DatabaseService) {
    this.repository = db.flowProcessSteps;
  }

  normalizeEntityValues(entity: Partial<FlowProcessStep | FlowProcessStepModel>): Partial<FlowProcessStep> {
    const flowProcessStep = new FlowProcessStep();
    Object.assign(flowProcessStep, entity);
    if (flowProcessStep.description === '') flowProcessStep.description = null;
    return flowProcessStep;
  }

  async findOneBy(options: FindOneOptions): Promise<FlowProcessStep | null> {
    const conditions: Record<string, unknown> = { ...options };

    if (conditions.processId) {
      conditions.flowProcess = { id: conditions.processId };
      delete conditions.processId;
    }

    if (conditions.parentId === null) {
      conditions.parentId = IsNull();
    }

    if (conditions.prevId === null) {
      conditions.prevId = IsNull();
    }

    return await this.repository.createQueryBuilder().where(conditions).getOne();
  }

  async findAll({ processId }: { processId: EntityId }): Promise<FlowProcessStep[]> {
    return await this.repository.find({
      where: {
        flowProcess: { id: processId },
      },
    });
  }

  async createFlowProcessStep(data: {
    stepProps: Pick<FlowProcessStepModel, 'name' | 'description' | 'parentId' | 'prevId' | 'mPath'>;
    processId: EntityId;
  }): Promise<FlowProcessStep> {
    const flowProcessStep = this.repository.create({
      ...this.normalizeEntityValues(data.stepProps),
      flowProcess: { id: data.processId },
    });
    return await this.repository.save(flowProcessStep);
  }

  async updateFlowProcessStep(
    flowProcessStep: FlowProcessStep,
    data: Partial<FlowProcessStep | FlowProcessStepModel>,
  ): Promise<FlowProcessStep> {
    const result = await this.repository.update(flowProcessStep.id, this.normalizeEntityValues(data));
    return result.affected ? Object.assign(flowProcessStep, data) : flowProcessStep;
  }

  async deleteFlowProcessStep(flowProcessStep: FlowProcessStep): Promise<FlowProcessStep> {
    await this.repository.delete(flowProcessStep.id);
    return flowProcessStep;
  }

  async findChildrenByMPath({
    processId,
    mPath,
  }: {
    processId: EntityId;
    mPath: string;
  }): Promise<FlowProcessStep[]> {
    return await this.repository.find({
      where: {
        flowProcess: { id: processId },
        mPath: Like(`${mPath}/%`),
      },
    });
  }

  async updateMPathForChildren(processId: EntityId, oldMPath: string, newMPath: string): Promise<void> {
    await this.repository.update(
      {
        flowProcess: { id: processId },
        mPath: Like(`${oldMPath}/%`),
      },
      {
        mPath: newMPath,
      },
    );
  }

  async deleteChildrenByMPath({ processId, mPath }: { processId: EntityId; mPath: string }): Promise<void> {
    await this.repository.delete({
      flowProcess: { id: processId },
      mPath: Like(`${mPath}/%`),
    });
  }
}
