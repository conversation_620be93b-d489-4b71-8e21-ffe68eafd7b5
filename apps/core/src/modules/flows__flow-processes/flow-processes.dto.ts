import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

const FlowProcessSchema = z.object({
  id: z.string().describe('Flow process id'),
  name: z.string().describe('Flow process name'),
  description: z.string().nullable().describe('Flow process description'),
  index: z.number().describe('Flow process index'),
  parentId: z.string().nullable().describe('Flow process parent id'),
});

export class FlowProcessDto extends createZodDto(FlowProcessSchema) {}

const CreateFlowProcessSchema = FlowProcessSchema.pick({
  name: true,
  description: true,
  parentId: true,
});

export class CreateFlowProcessDto extends createZodDto(CreateFlowProcessSchema) {}

const UpdateFlowProcessSchema = FlowProcessSchema.pick({
  name: true,
  description: true,
});

export class UpdateFlowProcessDto extends createZodDto(UpdateFlowProcessSchema) {}

const PartialUpdateFlowProcessSchema = UpdateFlowProcessSchema.partial();

export class PartialUpdateFlowProcessDto extends createZodDto(PartialUpdateFlowProcessSchema) {}
