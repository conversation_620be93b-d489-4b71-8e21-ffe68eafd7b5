import { Injectable } from '@nestjs/common';
import { Repository } from 'typeorm';
import { DatabaseService, FlowProcess } from '@core/database';

type FindOneOptions = {
  id?: EntityId;
};

@Injectable()
export class FlowProcessService {
  public readonly repository: Repository<FlowProcess>;

  constructor(private readonly db: DatabaseService) {
    this.repository = db.flowProcesses;
  }

  private normalizeEntityValues(entity: Partial<FlowProcess>): Partial<FlowProcess> {
    const flowProcess = new FlowProcess();
    Object.assign(flowProcess, entity);
    if (flowProcess.description === '') flowProcess.description = null;
    return flowProcess;
  }

  async findOneBy(options: FindOneOptions): Promise<FlowProcess | null> {
    return await this.repository.findOneBy(options);
  }

  async findAll({ flowId }: { flowId: EntityId }): Promise<FlowProcess[]> {
    return await this.repository.find({
      where: {
        flow: { id: flowId },
      },
    });
  }

  async findMaxFlowProcessIndex(): Promise<number> {
    const result = await this.repository
      .createQueryBuilder('entity')
      .select('MAX(entity.index)')
      .getRawOne<{ max: number }>();

    return result?.max ?? 0;
  }

  async createFlowProcess(data: {
    flowProcess: Pick<FlowProcess, 'name' | 'description' | 'index' | 'parentId'>;
    flowId: EntityId;
  }): Promise<FlowProcess> {
    const flowProcess = this.repository.create({
      ...this.normalizeEntityValues(data.flowProcess),
      flow: { id: data.flowId },
    });
    return await this.repository.save(flowProcess);
  }

  async updateFlowProcess(flowProcess: FlowProcess, data: Partial<FlowProcess>): Promise<FlowProcess> {
    const result = await this.repository.update(flowProcess.id, this.normalizeEntityValues(data));
    return result.affected ? Object.assign(flowProcess, data) : flowProcess;
  }

  async deleteFlowProcess(flowProcess: FlowProcess): Promise<FlowProcess> {
    await this.repository.delete(flowProcess.id);
    return flowProcess;
  }
}
