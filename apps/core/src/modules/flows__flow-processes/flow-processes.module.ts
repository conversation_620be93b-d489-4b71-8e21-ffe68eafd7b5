import { Module } from '@nestjs/common';
import { DatabaseModule } from '@core/database';
import { FlowProcessesController } from './flow-processes.controller';
import { FlowProcessService } from './flow-processes.service';
import {
  GetAllFlowProcessesUseCase,
  CreateFlowProcessUseCase,
  GetFlowProcessByIdUseCase,
  UpdateFlowProcessUseCase,
  DeleteFlowProcessByIdUseCase,
} from './use-cases';

@Module({
  imports: [DatabaseModule],
  providers: [
    FlowProcessService,

    GetAllFlowProcessesUseCase,
    CreateFlowProcessUseCase,
    GetFlowProcessByIdUseCase,
    UpdateFlowProcessUseCase,
    DeleteFlowProcessByIdUseCase,
  ],
  controllers: [FlowProcessesController],
})
export class FlowProcessesModule {}
