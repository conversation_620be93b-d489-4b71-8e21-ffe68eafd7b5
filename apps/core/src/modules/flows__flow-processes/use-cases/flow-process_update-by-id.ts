import { Injectable, NotFoundException } from '@nestjs/common';
import { FlowProcess } from '@core/database';
import { PartialUpdateFlowProcessDto, UpdateFlowProcessDto } from '../flow-processes.dto';
import { FlowProcessService } from '../flow-processes.service';

@Injectable()
export class UpdateFlowProcessUseCase implements UseCase {
  constructor(private readonly processService: FlowProcessService) {}

  async execute(id: EntityId, dto: UpdateFlowProcessDto | PartialUpdateFlowProcessDto): Promise<FlowProcess> {
    const { name, description } = dto;

    const flowProcess = await this.processService.findOneBy({ id });
    if (!flowProcess) throw new NotFoundException('Flow process not found', 'FLOW_PROCESS_NOT_FOUND');

    return await this.processService.updateFlowProcess(flowProcess, { name, description });
  }
}
