import { Injectable, NotFoundException } from '@nestjs/common';
import { FlowProcess } from '@core/database';
import { FlowProcessService } from '../flow-processes.service';

@Injectable()
export class DeleteFlowProcessByIdUseCase implements UseCase {
  constructor(private readonly processService: FlowProcessService) {}

  async execute(id: EntityId): Promise<FlowProcess> {
    const flowProcess = await this.processService.findOneBy({ id });

    if (!flowProcess) throw new NotFoundException('Flow process not found', 'FLOW_PROCESS_NOT_FOUND');

    return await this.processService.deleteFlowProcess(flowProcess);
  }
}
