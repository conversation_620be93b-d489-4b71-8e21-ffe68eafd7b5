import { Injectable } from '@nestjs/common';
import { FlowProcess } from '@core/database';
import { FlowProcessService } from '../flow-processes.service';

@Injectable()
export class GetAllFlowProcessesUseCase implements UseCase {
  constructor(private readonly processService: FlowProcessService) {}

  async execute({ flowId }: { flowId: EntityId }): Promise<FlowProcess[]> {
    return await this.processService.findAll({ flowId });
  }
}
