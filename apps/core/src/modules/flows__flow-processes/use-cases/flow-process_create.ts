import { Injectable } from '@nestjs/common';
import { FlowProcess } from '@core/database';
import { CreateFlowProcessDto } from '../flow-processes.dto';
import { FlowProcessService } from '../flow-processes.service';

@Injectable()
export class CreateFlowProcessUseCase implements UseCase {
  constructor(private readonly processService: FlowProcessService) {}

  async execute(flowId: EntityId, dto: CreateFlowProcessDto): Promise<FlowProcess> {
    const nextIndex = await this.processService.findMaxFlowProcessIndex();

    return await this.processService.createFlowProcess({
      flowProcess: { ...dto, index: nextIndex + 1 },
      flowId,
    });
  }
}
