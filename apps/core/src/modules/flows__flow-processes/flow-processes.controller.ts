import { Body, Controller, Delete, Get, HttpStatus, Param, Patch, Post, Put } from '@nestjs/common';
import { ApiParam } from '@nestjs/swagger';
import { ApiSuccessfulResponse, HttpResponse } from '@libs/common/api';
import {
  CreateFlowProcessDto,
  FlowProcessDto,
  PartialUpdateFlowProcessDto,
  UpdateFlowProcessDto,
} from './flow-processes.dto';
import {
  CreateFlowProcessUseCase,
  DeleteFlowProcessByIdUseCase,
  GetAllFlowProcessesUseCase,
  GetFlowProcessByIdUseCase,
  UpdateFlowProcessUseCase,
} from './use-cases';

const ApiParamFlowProcessId = () =>
  ApiParam({ name: 'processId', required: true, description: 'Flow process identifier', type: 'string' });

@Controller('flows/:flowId/processes')
@ApiParam({ name: 'flowId', required: true, description: 'Flow identifier', type: 'string' })
export class FlowProcessesController {
  constructor(
    private readonly createFlowProcessUseCase: CreateFlowProcessUseCase,
    private readonly updateFlowProcessUseCase: UpdateFlowProcessUseCase,
    private readonly deleteFlowProcessByIdUseCase: DeleteFlowProcessByIdUseCase,
    private readonly getFlowProcessByIdUseCase: GetFlowProcessByIdUseCase,
    private readonly getAllFlowProcessesUseCase: GetAllFlowProcessesUseCase,
  ) {}

  @Post()
  @ApiSuccessfulResponse(HttpStatus.CREATED, 'Flow process created', FlowProcessDto)
  async createFlowProcess(@Param('flowId') flowId: EntityId, @Body() dto: CreateFlowProcessDto) {
    const process = await this.createFlowProcessUseCase.execute(flowId, dto);
    return new HttpResponse({ data: process, message: 'Flow process created' });
  }

  @Put(':processId')
  @ApiParamFlowProcessId()
  @ApiSuccessfulResponse(HttpStatus.OK, 'Flow process updated', FlowProcessDto)
  async updateFlowProcess(
    @Param('flowId') flowId: EntityId,
    @Param('processId') processId: EntityId,
    @Body() dto: UpdateFlowProcessDto,
  ) {
    const process = await this.updateFlowProcessUseCase.execute(processId, dto);
    return new HttpResponse({ data: process, message: 'Flow process updated' });
  }

  @Patch(':processId')
  @ApiParamFlowProcessId()
  @ApiSuccessfulResponse(HttpStatus.OK, 'Flow process updated', FlowProcessDto)
  async partialUpdateFlowProcess(
    @Param('flowId') flowId: EntityId,
    @Param('processId') processId: EntityId,
    @Body() dto: PartialUpdateFlowProcessDto,
  ) {
    await this.updateFlowProcessUseCase.execute(processId, dto);
    return new HttpResponse({ message: 'Flow process updated' });
  }

  @Delete(':processId')
  @ApiParamFlowProcessId()
  @ApiSuccessfulResponse(HttpStatus.OK, 'Flow process deleted', FlowProcessDto)
  async deleteFlowProcess(@Param('flowId') flowId: EntityId, @Param('processId') processId: EntityId) {
    const process = await this.deleteFlowProcessByIdUseCase.execute(processId);
    return new HttpResponse({ data: process, message: 'Flow process deleted' });
  }

  @Get(':processId')
  @ApiParamFlowProcessId()
  @ApiSuccessfulResponse(HttpStatus.OK, 'Flow process found', FlowProcessDto)
  async getFlowProcess(@Param('flowId') flowId: EntityId, @Param('processId') processId: EntityId) {
    const process = await this.getFlowProcessByIdUseCase.execute(processId);
    return new HttpResponse({ data: process, message: 'Flow process found' });
  }

  @Get()
  @ApiSuccessfulResponse(HttpStatus.OK, 'Flow processes found', [FlowProcessDto])
  async getFlowProcesses(@Param('flowId') flowId: EntityId) {
    const processes = await this.getAllFlowProcessesUseCase.execute({ flowId });
    return new HttpResponse({ data: processes, message: 'Flow processes found' });
  }
}
