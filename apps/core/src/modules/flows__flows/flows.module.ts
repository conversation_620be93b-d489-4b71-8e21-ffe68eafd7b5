import { Module } from '@nestjs/common';
import { DatabaseModule } from '@core/database';
import { FlowsController } from './flows.controller';
import { FlowService } from './flows.service';
import {
  GetAllFlowsUseCase,
  CreateFlowUseCase,
  GetFlowByIdUseCase,
  UpdateFlowUseCase,
  DeleteFlowByIdUseCase,
} from './use-cases';

@Module({
  imports: [DatabaseModule],
  providers: [
    FlowService,

    GetAllFlowsUseCase,
    CreateFlowUseCase,
    GetFlowByIdUseCase,
    UpdateFlowUseCase,
    DeleteFlowByIdUseCase,
  ],
  controllers: [FlowsController],
})
export class FlowsModule {}
