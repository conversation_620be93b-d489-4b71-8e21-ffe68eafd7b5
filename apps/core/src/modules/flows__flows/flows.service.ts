import { Injectable } from '@nestjs/common';
import { Repository } from 'typeorm';
import { DatabaseService, Flow } from '@core/database';

type FindOneOptions = {
  id?: EntityId;
};

@Injectable()
export class FlowService {
  public readonly repository: Repository<Flow>;

  constructor(private readonly db: DatabaseService) {
    this.repository = db.flows;
  }

  private normalizeEntityValues(entity: Partial<Flow>): Partial<Flow> {
    const flow = new Flow();
    Object.assign(flow, entity);
    if (flow.description === '') flow.description = null;
    return flow;
  }

  async findOneBy(options: FindOneOptions): Promise<Flow | null> {
    return await this.repository.findOneBy(options);
  }

  async findAll(): Promise<Flow[]> {
    return await this.repository.find();
  }

  async findMaxFlowIndex(): Promise<number> {
    const result = await this.repository
      .createQueryBuilder('entity')
      .select('MAX(entity.index)')
      .getRawOne<{ max: number }>();

    return result?.max ?? 0;
  }

  async createFlow(data: Pick<Flow, 'name' | 'description' | 'index'>): Promise<Flow> {
    const flow = this.repository.create(this.normalizeEntityValues(data));
    return await this.repository.save(flow);
  }

  async updateFlow(flow: Flow, data: Partial<Flow>): Promise<Flow> {
    const result = await this.repository.update(flow.id, this.normalizeEntityValues(data));
    return result.affected ? Object.assign(flow, data) : flow;
  }

  async deleteFlow(flow: Flow): Promise<Flow> {
    await this.repository.delete(flow.id);
    return flow;
  }
}
