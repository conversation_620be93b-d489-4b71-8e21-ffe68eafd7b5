import { Body, Controller, Delete, Get, HttpStatus, Param, Patch, Post, Put } from '@nestjs/common';
import { ApiParam } from '@nestjs/swagger';
import { ApiSuccessfulResponse, HttpResponse, SanitizeResponseWithZod } from '@libs/common/api';
import { CreateFlowDto, FlowDto, FlowSchema, PartialUpdateFlowDto, UpdateFlowDto } from './flows.dto';
import {
  CreateFlowUseCase,
  DeleteFlowByIdUseCase,
  GetAllFlowsUseCase,
  GetFlowByIdUseCase,
  UpdateFlowUseCase,
} from './use-cases';

const ApiParamFlowId = () =>
  ApiParam({ name: 'flowId', required: true, description: 'Flow identifier', type: 'string' });

@Controller('flows')
export class FlowsController {
  constructor(
    private readonly createFlowUseCase: CreateFlowUseCase,
    private readonly updateFlowUseCase: UpdateFlowUseCase,
    private readonly deleteFlowByIdUseCase: DeleteFlowByIdUseCase,
    private readonly getFlowByIdUseCase: GetFlowByIdUseCase,
    private readonly getAllFlowsUseCase: GetAllFlowsUseCase,
  ) {}

  @Post()
  @ApiSuccessfulResponse(HttpStatus.CREATED, 'Flow created', FlowDto)
  async createFlow(@Body() dto: CreateFlowDto) {
    const flow = await this.createFlowUseCase.execute(dto);
    return new HttpResponse({ data: flow, message: 'Flow created' });
  }

  @Put(':flowId')
  @ApiParamFlowId()
  @ApiSuccessfulResponse(HttpStatus.OK, 'Flow updated', FlowDto)
  async updateFlow(@Param('flowId') flowId: EntityId, @Body() dto: UpdateFlowDto) {
    const flow = await this.updateFlowUseCase.execute(flowId, dto);
    return new HttpResponse({ data: flow, message: 'Flow updated' });
  }

  @Patch(':flowId')
  @ApiParamFlowId()
  @ApiSuccessfulResponse(HttpStatus.OK, 'Flow updated', FlowDto)
  async partialUpdateFlow(@Param('flowId') flowId: EntityId, @Body() dto: PartialUpdateFlowDto) {
    const flow = await this.updateFlowUseCase.execute(flowId, dto);
    return new HttpResponse({ data: flow, message: 'Flow updated' });
  }

  @Delete(':flowId')
  @ApiParamFlowId()
  @ApiSuccessfulResponse(HttpStatus.OK, 'Flow deleted', FlowDto)
  async deleteFlow(@Param('flowId') flowId: EntityId) {
    const flow = await this.deleteFlowByIdUseCase.execute(flowId);
    return new HttpResponse({ data: flow, message: 'Flow deleted' });
  }

  @Get(':flowId')
  @SanitizeResponseWithZod(FlowSchema)
  @ApiParamFlowId()
  @ApiSuccessfulResponse(HttpStatus.OK, 'Flow found', FlowDto)
  async getFlow(@Param('flowId') flowId: EntityId) {
    const flow = await this.getFlowByIdUseCase.execute(flowId);
    return new HttpResponse({ data: flow, message: 'Flow found' });
  }

  @Get()
  @ApiSuccessfulResponse(HttpStatus.OK, 'Flows found', [FlowDto])
  async getAllFlows() {
    const flows = await this.getAllFlowsUseCase.execute();
    return new HttpResponse({ data: flows, message: 'Flows found' });
  }
}
