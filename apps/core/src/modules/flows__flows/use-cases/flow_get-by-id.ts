import { Injectable, NotFoundException } from '@nestjs/common';
import { Flow } from '@core/database';
import { FlowService } from '../flows.service';

@Injectable()
export class GetFlowByIdUseCase implements UseCase {
  constructor(private readonly flowService: FlowService) {}

  async execute(id: EntityId): Promise<Flow> {
    const flow = await this.flowService.findOneBy({ id });

    if (!flow) throw new NotFoundException('Flow not found', 'FLOW_NOT_FOUND');

    return flow;
  }
}
