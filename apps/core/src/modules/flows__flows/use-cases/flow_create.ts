import { Injectable } from '@nestjs/common';
import { Flow } from '@core/database';
import { CreateFlowDto } from '../flows.dto';
import { FlowService } from '../flows.service';

@Injectable()
export class CreateFlowUseCase implements UseCase {
  constructor(private readonly flowService: FlowService) {}

  async execute(dto: CreateFlowDto): Promise<Flow> {
    const { name, description } = dto;

    const nextIndex = await this.flowService.findMaxFlowIndex();

    return await this.flowService.createFlow({ name, description, index: nextIndex + 1 });
  }
}
