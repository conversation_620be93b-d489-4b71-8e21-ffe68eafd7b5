import { Logger, Mo<PERSON><PERSON> } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { isDevelopment } from '@libs/common/envs';
import { EnvironmentVariables } from '../config';
import { DatabaseService } from './database.service';
import { Flow, FlowProcess, FlowProcessStep } from './flows';

@Module({
  imports: [
    TypeOrmModule.forRootAsync({
      inject: [ConfigService, Logger],
      useFactory: (config: ConfigService<EnvironmentVariables>, logger: Logger) => {
        console.log(logger);
        console.log(logger);
        return {
          type: 'postgres',
          host: config.get('PG_HOST'),
          port: config.get('PG_PORT'),
          password: config.get('PG_PASSWORD'),
          username: config.get('PG_USER'),
          database: config.get('PG_DB'),
          // entities: [Flow, FlowProcess, FlowProcessStep],
          autoLoadEntities: true,
          // synchronize: true,
          logging: isDevelopment(),
          // logger: logger,
        };
      },
    }),

    TypeOrmModule.forFeature([Flow, FlowProcess, FlowProcessStep]),
  ],

  providers: [DatabaseService],
  exports: [DatabaseService],
})
export class DatabaseModule {}
