import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { FlowProcessModel } from '@core/models';
import { FlowProcessStep } from './flow-process-step.entity';
import { Flow } from './flow.entity';

@Entity('flow-processes')
export class FlowProcess implements UnknownProperties<FlowProcessModel> {
  @PrimaryGeneratedColumn()
  id: EntityId;

  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string | null;

  @Column({ type: 'smallint' })
  index: number;

  @Column({ type: 'integer', nullable: true })
  parentId: EntityId | null;

  @ManyToOne(() => Flow, flow => flow.processes, {
    nullable: false,
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'flowId', referencedColumnName: 'id' })
  flow: Flow;

  @OneToMany(() => FlowProcessStep, step => step.flowProcess)
  steps: FlowProcessStep[];
}
