import { Column, <PERSON>tity, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { FlowModel } from '@core/models';
import { FlowProcess } from './flow-process.entity';

@Entity('flows')
export class Flow implements UnknownProperties<FlowModel> {
  @PrimaryGeneratedColumn()
  id: EntityId;

  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string | null;

  @Column({ type: 'smallint' })
  index: number;

  @OneToMany(() => FlowProcess, process => process.flow)
  processes: FlowProcess[];

  // @Column({ type: 'integer', nullable: true })
  // parentId: EntityId | null;

  // // TODO: do we really need these fields
  // @CreateDateColumn({ name: 'created_at' })
  // createdAt: Date;

  // @UpdateDateColumn({ name: 'updated_at' })
  // updatedAt: Date;
}
