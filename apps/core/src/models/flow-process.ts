import { z } from 'zod';

export const FlowProcessSchema = z.object({
  id: z.string().describe('Flow process id'),
  name: z.string().describe('Flow process name'),
  description: z.string().nullable().describe('Flow process description'),
  index: z.number().describe('Flow process index'),
  parentId: z.string().nullable().describe('Flow process parent id'),
});

export type FlowProcessModel = z.infer<typeof FlowProcessSchema>;
