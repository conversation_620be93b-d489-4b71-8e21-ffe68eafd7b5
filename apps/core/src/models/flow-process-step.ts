import { z } from 'zod';

export const FlowProcessStepSchema = z.object({
  id: z.string().describe('Flow process step id'),
  name: z.string().describe('Flow process step name'),
  description: z.string().nullable().describe('Flow process step description'),
  parentId: z.string().nullable().describe('Flow process step parent id'),
  prevId: z.string().nullable().describe('Flow process step previous id'),
  mPath: z.string().describe('Flow process step materialized path'),
});

export type FlowProcessStepModel = z.infer<typeof FlowProcessStepSchema>;
