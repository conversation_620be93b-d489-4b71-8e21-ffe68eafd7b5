import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable, map } from 'rxjs';
import { ZodSchema } from 'zod';

// https://artosalminen.github.io/posts/how-to-manipulate-nestjs-response

export const SanitizeResponseWithZod = Reflector.createDecorator<ZodSchema>();

// TODO: arrays

@Injectable()
export class ZodResponseInterceptor<T extends object> implements NestInterceptor<T> {
  constructor(private reflector: Reflector) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<T> {
    const schema = this.reflector.get(SanitizeResponseWithZod, context.getHandler());
    return next.handle().pipe(
      map((data: T) => {
        if (!schema) return data;
        const isHttpResponse = 'data' in data;
        const payload = isHttpResponse ? data.data : data;
        const parsedData = schema.parse(payload) as unknown;
        return (isHttpResponse ? { ...data, data: parsedData } : parsedData) as T;
      }),
    );
  }
}
